=== NHR Trello Skin for FluentBoards ===
Contributors: nhrrob
Tags: skin, kanban, trello board, task management, fluent boards
Requires at least: 6.0
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 1.1.2
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Trello like skin for FluentBoards

== Description ==
- 🚀 [GitHub Repository](https://github.com/nhrrob/nhrrob-options-table-manager): Found a bug or have a feature request? Let us know!
- 💬 [Slack Community](https://join.slack.com/t/nhrrob/shared_invite/zt-2m3nyrl1f-eKv7wwJzsiALcg0nY6~e0Q): Got questions or just want to chat? Come hang out with us on Slack!

`<?php echo 'Trello Skin for FluentBoards'; ?>`

### 🌟 Lightweight Performance
We understand the importance of a fast-loading website. That's why our plugin is designed to have minimal impact on your site's performance, ensuring a seamless user experience for you and your visitors.

### 💬 Join Thousands of Happy Users
Join the growing community of WordPress users who trust NHR Trello Skin for FluentBoards to streamline their dashboard experience. Install it today and enjoy the trello feel on FluentBoards like never before!

### Improvements
1. Add new lists button
2. Replace settings icon with three dots on lists header
3. Remove plus icon from list header
4. List header height
5. list item height width border
6. List header count border radius
7. List item settings icon show on hover
8. List item bottom spacing
9. List footer design
10. List footer text change to 'Add a card'
11. List footer quick task creation popup design
12. List header settings popup design 

== Installation ==

Upload the NHR Trello Skin for FluentBoards plugin to your blog, activate it.

1, 2, 3: You're done!


== Frequently Asked Questions ==

= Does it require any other plugin =

Yes. It works as an addon for FluentBoards plugin.

= Will using NHR Trello Skin for FluentBoards impact my website's performance? =
No, NHR Trello Skin for FluentBoards is built with performance in mind and has a minimal impact on your website's speed and performance. 
The plugin's lightweight code ensures that it operates efficiently without slowing down your WordPress dashboard or affecting your site's loading times.


== Screenshots ==

1. Before installing the plugin.
2. After installing the plugin. All notices are removed.
3. Gif to demonstrate existing improvements

== Changelog ==

= 1.1.2 - 02/06/2025 =
- Plugin installation not working! New release to regenerate the build zip
- Few more improvements

= 1.1.1 - 30/03/2025 =
- WordPress tested up to version is updated to 6.8
- Few more improvements

= 1.1.0 - 30/10/2024 =
- Added: Item Modal | Action button hover effect
- Added: Item Modal | Label hover effect
- Added: Gif demonstrating the latest features
- Few more improvements

= 1.0.9 - 28/10/2024 =
- Added: Item Modal - comments all tab hide
- Added: Item Modal - comment box/panel design
- Few more improvements

= 1.0.8 - 26/10/2024 =
- Added: List header settings - popup design
- Added: Add another list - form design
- Added: Item Modal | design
- Few more improvements

= 1.0.7 - 23/10/2024 =
- Revamped: List header actions popup design
- Few more improvements

= 1.0.6 - 22/10/2024 =
- Added: List item avatar on the right size
- Added: List item inner spacing
- Added: List item avatar hide
- Added: Re order dates, comment, avatar
- Added: List item comment count icon
- Added: List item label content spaciing
- Updated: List width same as Trello list
- Updated: Comment icon and count gap increase
- Updated: Label space reduce
- Few more improvements

= 1.0.5 - 20/10/2024 =
- Added: new lists button
- Added: List header height
- Added: list item height width border
- Added: List header count border radius
- Added: List item settings icon show on hover
- Added: List footer design
- Added: List footer quick task creation popup design
- Updated: List footer text change to 'Add a card'
- Updated: Replace settings icon with three dots on lists header
- Updated: Remove plus icon from list header
- Updated: List item bottom spacing
- Few more improvements

= 1.0.4 - 19/10/2024 =
- Added: Multiple improvements on the list and list item design

= 1.0.3 - 19/10/2024 =
- Fixed: Plugin banners naming issue fixed

= 1.0.2 - 18/10/2024 =
- Fixed: Plugin Check Plugin issues

= 1.0.1 - 18/10/2024 =
- Added: Composer json file

= 1.0.0 - 18/10/2024 =
- Initial beta release. Cheers!!


== Upgrade Notice ==

= 1.0.0 =
- This is the initial release. Feel free to share any feature request at the plugin support forum page.