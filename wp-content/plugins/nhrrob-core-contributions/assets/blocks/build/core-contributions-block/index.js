(()=>{"use strict";var e={n:o=>{var n=o&&o.__esModule?()=>o.default:()=>o;return e.d(n,{a:n}),n},d:(o,n)=>{for(var r in n)e.o(n,r)&&!e.o(o,r)&&Object.defineProperty(o,r,{enumerable:!0,get:n[r]})},o:(e,o)=>Object.prototype.hasOwnProperty.call(e,o)};const o=window.wp.blocks,n=window.wp.blockEditor,r=window.wp.components,t=window.wp.element,l=window.wp.apiFetch;var a=e.n(l);const i=JSON.parse('{"UU":"nhrcc-core-contributions/core-contributions-block"}'),s=window.ReactJSXRuntime;(0,o.registerBlockType)(i.UU,{edit:function({attributes:e,setAttributes:o}){const l=(0,n.useBlockProps)({style:{backgroundColor:e.backgroundColor,color:e.textColor,padding:e.padding?`${e.padding.top||0} ${e.padding.right||0} ${e.padding.bottom||0} ${e.padding.left||0}`:void 0,margin:e.margin?`${e.margin.top||0} ${e.margin.right||0} ${e.margin.bottom||0} ${e.margin.left||0}`:void 0,borderColor:e.borderColor,borderRadius:e.borderRadius?`${e.borderRadius}px`:void 0,fontSize:e.fontSize,fontWeight:e.fontWeight}}),[i,g]=(0,t.useState)(e.username),[b,p]=(0,t.useState)(""),[m,C]=(0,t.useState)(!1),h=(0,t.useRef)(function(){let e;return(...n)=>{clearTimeout(e),e=setTimeout((()=>(e=>{o({username:e})})(...n)),500)}}()).current;return(0,t.useEffect)((()=>{e.username||o({username:nhrccCoreContributionsCommonObj?.nhrccSettings?.username||"",preset:nhrccCoreContributionsCommonObj?.nhrccSettings?.preset||"default"})}),[]),(0,t.useEffect)((()=>{e.username?(C(!0),async function(e){try{return(await a()({path:"/nhrcc-core-contributions/v1/core-contributions/render",method:"POST",data:e})).content||""}catch(e){throw console.error("Failed to fetch preview:",e),new Error("Failed to load preview")}}(e).then(p).catch((e=>{console.error("Failed to load preview:",e),p(`Error: ${e.message}`)})).finally((()=>C(!1)))):p("")}),[e.username,e.preset,e.backgroundColor,e.textColor,e.linkColor,e.borderColor,e.borderRadius,e.padding,e.margin,e.fontSize,e.fontWeight]),(0,s.jsxs)("div",{...l,children:[(0,s.jsxs)(n.InspectorControls,{children:[(0,s.jsxs)(r.PanelBody,{title:"Core Contributions Settings",children:[(0,s.jsx)(r.TextControl,{label:"WordPress.org Username",value:i,onChange:e=>{g(e),h(e)},help:"Enter your WordPress.org username to display contributions"}),(0,s.jsx)(r.SelectControl,{label:"Design Style",value:e.preset,options:d,onChange:e=>o({preset:e})})]}),(0,s.jsx)(n.PanelColorSettings,{title:"Color Settings",colorSettings:[{value:e.backgroundColor,onChange:e=>o({backgroundColor:e}),label:"Background Color"},{value:e.textColor,onChange:e=>o({textColor:e}),label:"Text Color"},{value:e.linkColor,onChange:e=>o({linkColor:e}),label:"Link Color"},{value:e.borderColor,onChange:e=>o({borderColor:e}),label:"Border Color"}]}),(0,s.jsx)(r.PanelBody,{title:"Spacing Settings",initialOpen:!1,children:r.__experimentalBoxControl&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.__experimentalBoxControl,{label:"Padding",values:e.padding,onChange:e=>o({padding:e})}),(0,s.jsx)(r.__experimentalBoxControl,{label:"Margin",values:e.margin,onChange:e=>o({margin:e})})]})}),(0,s.jsx)(r.PanelBody,{title:"Border Settings",initialOpen:!1,children:(0,s.jsx)(r.RangeControl,{label:"Border Radius",value:e.borderRadius||0,onChange:e=>o({borderRadius:e}),min:0,max:50})}),(0,s.jsxs)(r.PanelBody,{title:"Typography Settings",initialOpen:!1,children:[r.__experimentalUnitControl&&(0,s.jsx)(r.__experimentalUnitControl,{label:"Font Size",value:e.fontSize,onChange:e=>o({fontSize:e})}),(0,s.jsx)(r.SelectControl,{label:"Font Weight",value:e.fontWeight,options:u,onChange:e=>o({fontWeight:e})})]})]}),(0,s.jsx)(c,{isLoading:m,username:e.username,content:b})]})},save:()=>null});const d=[{label:"Default",value:"default"},{label:"Minimal",value:"minimal"}],u=[{label:"Normal",value:"normal"},{label:"Bold",value:"bold"},{label:"Light",value:"300"},{label:"Medium",value:"500"},{label:"Semi Bold",value:"600"},{label:"Extra Bold",value:"800"}];function c({isLoading:e,username:o,content:n}){return e?(0,s.jsx)(r.Spinner,{}):o?(0,s.jsx)("div",{className:"nhr-core-contributions-preview",dangerouslySetInnerHTML:{__html:n}}):(0,s.jsx)("div",{className:"components-placeholder",children:(0,s.jsx)("p",{children:"Please set a WordPress.org username to preview the contributions."})})}})();