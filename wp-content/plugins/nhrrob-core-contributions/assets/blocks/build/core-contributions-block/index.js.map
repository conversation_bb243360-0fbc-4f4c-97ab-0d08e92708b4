{"version": 3, "file": "core-contributions-block/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;ACNsD;AAIrB;AACsD;AACtB;AACrB;AACR;AAAA;AAEpC,SAASgB,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC3B,IAAIC,SAAS;EACb,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChBC,YAAY,CAACF,SAAS,CAAC;IACvBA,SAAS,GAAGG,UAAU,CAAC,MAAML,IAAI,CAAC,GAAGG,IAAI,CAAC,EAAEF,KAAK,CAAC;EACtD,CAAC;AACL;AAEAlB,oEAAiB,CAACW,6CAAa,EAAE;EAC7Ba,IAAI,EAAEC,aAAa;EACnBC,IAAI,EAAEA,CAAA,KAAM,IAAI,CAAE;AACtB,CAAC,CAAC;AAEF,SAASD,aAAaA,CAAC;EAAEE,UAAU;EAAEC;AAAc,CAAC,EAAE;EAClD,MAAMC,UAAU,GAAG5B,sEAAa,CAAC,CAAC;EAClC,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAGxB,4DAAQ,CAACoB,UAAU,CAACK,QAAQ,CAAC;EACrE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,4DAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,4DAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM8B,uBAAuB,GAAG5B,0DAAM,CAClCO,QAAQ,CAAEgB,QAAQ,IAAK;IACnBJ,aAAa,CAAC;MAAEI;IAAS,CAAC,CAAC;EAC/B,CAAC,EAAE,GAAG,CACV,CAAC,CAACM,OAAO;;EAET;EACA9B,6DAAS,CAAC,MAAM;IACZ,IAAI,CAACmB,UAAU,CAACK,QAAQ,EAAE;MACtBJ,aAAa,CAAC;QACVI,QAAQ,EAAEO,+BAA+B,EAAEC,aAAa,EAAER,QAAQ,IAAI,EAAE;QACxES,MAAM,EAAEF,+BAA+B,EAAEC,aAAa,EAAEC,MAAM,IAAI;MACtE,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERjC,6DAAS,CAAC,MAAM;IACZ,IAAI,CAACmB,UAAU,CAACK,QAAQ,EAAE;MACtB;MACAE,iBAAiB,CAAC,EAAE,CAAC;MACrB;IACJ;IAEAE,YAAY,CAAC,IAAI,CAAC;IAElBM,YAAY,CAACf,UAAU,CAAC,CACnBgB,IAAI,CAACT,iBAAiB,CAAC,CACvBU,KAAK,CAAEC,KAAK,IAAK;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CX,iBAAiB,CAAC,UAAUW,KAAK,CAACE,OAAO,EAAE,CAAC;IAChD,CAAC,CAAC,CACDC,OAAO,CAAC,MAAMZ,YAAY,CAAC,KAAK,CAAC,CAAC;EAC3C,CAAC,EAAE,CAACT,UAAU,CAACK,QAAQ,EAAEL,UAAU,CAACc,MAAM,CAAC,CAAC;EAE5C,oBACI1B,uDAAA;IAAA,GAASc,UAAU;IAAAoB,QAAA,gBACfpC,sDAAA,CAACX,sEAAiB;MAAA+C,QAAA,eACdlC,uDAAA,CAACZ,4DAAS;QAAC+C,KAAK,EAAC,6BAA6B;QAAAD,QAAA,gBAC1CpC,sDAAA,CAACT,8DAAW;UACR+C,KAAK,EAAC,wBAAwB;UAC9BC,KAAK,EAAEtB,YAAa;UACpBuB,QAAQ,EAAGrB,QAAQ,IAAK;YACpBD,eAAe,CAACC,QAAQ,CAAC;YACzBK,uBAAuB,CAACL,QAAQ,CAAC;UACrC,CAAE;UACFsB,IAAI,EAAC;QAA4D,CACpE,CAAC,eACFzC,sDAAA,CAACR,gEAAa;UACV8C,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEzB,UAAU,CAACc,MAAO;UACzBc,OAAO,EAAEC,cAAe;UACxBH,QAAQ,EAAGZ,MAAM,IAAKb,aAAa,CAAC;YAAEa;UAAO,CAAC;QAAE,CACnD,CAAC;MAAA,CACK;IAAC,CACG,CAAC,eAEpB5B,sDAAA,CAAC4C,cAAc;MACXtB,SAAS,EAAEA,SAAU;MACrBH,QAAQ,EAAEL,UAAU,CAACK,QAAS;MAC9B0B,OAAO,EAAEzB;IAAe,CAC3B,CAAC;EAAA,CACD,CAAC;AAEd;;AAEA;AACA,MAAMuB,cAAc,GAAG,CACnB;EAAEL,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAU,CAAC,EACtC;EAAED,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAU,CAAC,CACzC;;AAED;AACA,eAAeV,YAAYA,CAACf,UAAU,EAAE;EACpC,IAAI;IACA,MAAMgC,QAAQ,GAAG,MAAMjD,2DAAQ,CAAC;MAC5BkD,IAAI,EAAE,wDAAwD;MAC9DC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEnC;IACV,CAAC,CAAC;IACF,OAAOgC,QAAQ,CAACD,OAAO,IAAI,EAAE;EACjC,CAAC,CAAC,OAAOb,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAM,IAAIkB,KAAK,CAAC,wBAAwB,CAAC;EAC7C;AACJ;;AAEA;AACA,SAASN,cAAcA,CAAC;EAAEtB,SAAS;EAAEH,QAAQ;EAAE0B;AAAQ,CAAC,EAAE;EACtD,IAAIvB,SAAS,EAAE;IACX,oBAAOtB,sDAAA,CAACP,0DAAO,IAAE,CAAC;EACtB;EAEA,IAAI,CAAC0B,QAAQ,EAAE;IACX,oBACInB,sDAAA;MAAKmD,SAAS,EAAC,wBAAwB;MAAAf,QAAA,eACnCpC,sDAAA;QAAAoC,QAAA,EAAG;MAAiE,CAAG;IAAC,CACvE,CAAC;EAEd;EAEA,oBACIpC,sDAAA;IACImD,SAAS,EAAC,gCAAgC;IAC1CC,uBAAuB,EAAE;MAAEC,MAAM,EAAER;IAAQ;EAAE,CAChD,CAAC;AAEV,C", "sources": ["webpack://nhrrob-core-contributions/external window [\"wp\",\"apiFetch\"]", "webpack://nhrrob-core-contributions/external window [\"wp\",\"blockEditor\"]", "webpack://nhrrob-core-contributions/external window [\"wp\",\"blocks\"]", "webpack://nhrrob-core-contributions/external window [\"wp\",\"components\"]", "webpack://nhrrob-core-contributions/external window [\"wp\",\"element\"]", "webpack://nhrrob-core-contributions/external window \"ReactJSXRuntime\"", "webpack://nhrrob-core-contributions/webpack/bootstrap", "webpack://nhrrob-core-contributions/webpack/runtime/compat get default export", "webpack://nhrrob-core-contributions/webpack/runtime/define property getters", "webpack://nhrrob-core-contributions/webpack/runtime/hasOwnProperty shorthand", "webpack://nhrrob-core-contributions/webpack/runtime/make namespace object", "webpack://nhrrob-core-contributions/./assets/blocks/src/core-contributions-block/index.js"], "sourcesContent": ["module.exports = window[\"wp\"][\"apiFetch\"];", "module.exports = window[\"wp\"][\"blockEditor\"];", "module.exports = window[\"wp\"][\"blocks\"];", "module.exports = window[\"wp\"][\"components\"];", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"ReactJSXRuntime\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { registerBlockType } from '@wordpress/blocks';\nimport {\n    useBlockProps,\n    InspectorControls,\n} from '@wordpress/block-editor';\nimport { PanelBody, TextControl, SelectControl, Spinner } from '@wordpress/components';\nimport { useState, useEffect, useRef } from '@wordpress/element';\nimport apiFetch from '@wordpress/api-fetch';\nimport metadata from './block.json';\n\nfunction debounce(func, delay) {\n    let timeoutId;\n    return (...args) => {\n        clearTimeout(timeoutId);\n        timeoutId = setTimeout(() => func(...args), delay);\n    };\n}\n\nregisterBlockType(metadata.name, {\n    edit: EditComponent,\n    save: () => null, // Use dynamic rendering on PHP side\n});\n\nfunction EditComponent({ attributes, setAttributes }) {\n    const blockProps = useBlockProps();\n    const [tempUsername, setTempUsername] = useState(attributes.username);\n    const [previewContent, setPreviewContent] = useState('');\n    const [isLoading, setIsLoading] = useState(false);\n\n    // Create debounced function with useRef to maintain reference\n    const updateUsernameDebounced = useRef(\n        debounce((username) => {\n            setAttributes({ username });\n        }, 500)\n    ).current;\n\n    // Separate effect for setting initial values\n    useEffect(() => {\n        if (!attributes.username) {\n            setAttributes({ \n                username: nhrccCoreContributionsCommonObj?.nhrccSettings?.username || '',\n                preset: nhrccCoreContributionsCommonObj?.nhrccSettings?.preset || 'default'\n            });\n        }\n    }, []); // Empty dependency array - runs once\n\n    useEffect(() => {\n        if (!attributes.username) {\n            // setAttributes({ username: nhrccCoreContributionsCommonObj?.nhrccSettings?.username });\n            setPreviewContent('');\n            return;\n        }\n\n        setIsLoading(true);\n\n        fetchPreview(attributes)\n            .then(setPreviewContent)\n            .catch((error) => {\n                console.error('Failed to load preview:', error);\n                setPreviewContent(`Error: ${error.message}`);\n            })\n            .finally(() => setIsLoading(false));\n    }, [attributes.username, attributes.preset]);\n\n    return (\n        <div {...blockProps}>\n            <InspectorControls>\n                <PanelBody title=\"Core Contributions Settings\">\n                    <TextControl\n                        label=\"WordPress.org Username\"\n                        value={tempUsername}\n                        onChange={(username) => {\n                            setTempUsername(username);\n                            updateUsernameDebounced(username);\n                        }}\n                        help=\"Enter your WordPress.org username to display contributions\"\n                    />\n                    <SelectControl\n                        label=\"Design Style\"\n                        value={attributes.preset}\n                        options={PRESET_OPTIONS}\n                        onChange={(preset) => setAttributes({ preset })}\n                    />\n                </PanelBody>\n            </InspectorControls>\n            \n            <PreviewContent \n                isLoading={isLoading}\n                username={attributes.username}\n                content={previewContent}\n            />\n        </div>\n    );\n}\n\n// Constants\nconst PRESET_OPTIONS = [\n    { label: 'Default', value: 'default' },\n    { label: 'Minimal', value: 'minimal' },\n];\n\n// API and Helper Functions\nasync function fetchPreview(attributes) {\n    try {\n        const response = await apiFetch({\n            path: '/nhrcc-core-contributions/v1/core-contributions/render',\n            method: 'POST',\n            data: attributes,\n        });\n        return response.content || '';\n    } catch (error) {\n        console.error('Failed to fetch preview:', error);\n        throw new Error('Failed to load preview');\n    }\n}\n\n// Presentational Components\nfunction PreviewContent({ isLoading, username, content }) {\n    if (isLoading) {\n        return <Spinner />;\n    }\n    \n    if (!username) {\n        return (\n            <div className=\"components-placeholder\">\n                <p>Please set a WordPress.org username to preview the contributions.</p>\n            </div>\n        );\n    }\n    \n    return (\n        <div \n            className=\"nhr-core-contributions-preview\"\n            dangerouslySetInnerHTML={{ __html: content }}\n        />\n    );\n}"], "names": ["registerBlockType", "useBlockProps", "InspectorCont<PERSON><PERSON>", "PanelBody", "TextControl", "SelectControl", "Spinner", "useState", "useEffect", "useRef", "apiFetch", "metadata", "jsx", "_jsx", "jsxs", "_jsxs", "debounce", "func", "delay", "timeoutId", "args", "clearTimeout", "setTimeout", "name", "edit", "EditComponent", "save", "attributes", "setAttributes", "blockProps", "tempUsername", "setTempUsername", "username", "previewContent", "setPreviewContent", "isLoading", "setIsLoading", "updateUsernameDebounced", "current", "nhrccCoreContributionsCommonObj", "nhrccSettings", "preset", "fetchPreview", "then", "catch", "error", "console", "message", "finally", "children", "title", "label", "value", "onChange", "help", "options", "PRESET_OPTIONS", "PreviewContent", "content", "response", "path", "method", "data", "Error", "className", "dangerouslySetInnerHTML", "__html"], "sourceRoot": ""}