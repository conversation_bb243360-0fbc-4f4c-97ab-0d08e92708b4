import { registerBlockType } from '@wordpress/blocks';
import {
    useBlockProps,
    InspectorControls,
    PanelColorSettings,
    withColors,
    getColorClassName,
    getColorObjectByColorValue,
} from '@wordpress/block-editor';
import {
    PanelBody,
    TextControl,
    SelectControl,
    Spinner,
    RangeControl,
    __experimentalBoxControl as BoxControl,
    __experimentalUnitControl as UnitControl
} from '@wordpress/components';
import { useState, useEffect, useRef } from '@wordpress/element';
import apiFetch from '@wordpress/api-fetch';
import metadata from './block.json';

function debounce(func, delay) {
    let timeoutId;
    return (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func(...args), delay);
    };
}

registerBlockType(metadata.name, {
    edit: EditComponent,
    save: () => null, // Use dynamic rendering on PHP side
});

function EditComponent({ attributes, setAttributes }) {
    const blockProps = useBlockProps({
        style: {
            backgroundColor: attributes.backgroundColor,
            color: attributes.textColor,
            padding: attributes.padding ? `${attributes.padding.top || 0} ${attributes.padding.right || 0} ${attributes.padding.bottom || 0} ${attributes.padding.left || 0}` : undefined,
            margin: attributes.margin ? `${attributes.margin.top || 0} ${attributes.margin.right || 0} ${attributes.margin.bottom || 0} ${attributes.margin.left || 0}` : undefined,
            borderColor: attributes.borderColor,
            borderRadius: attributes.borderRadius ? `${attributes.borderRadius}px` : undefined,
            fontSize: attributes.fontSize,
            fontWeight: attributes.fontWeight,
        }
    });
    const [tempUsername, setTempUsername] = useState(attributes.username);
    const [previewContent, setPreviewContent] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    // Create debounced function with useRef to maintain reference
    const updateUsernameDebounced = useRef(
        debounce((username) => {
            setAttributes({ username });
        }, 500)
    ).current;

    // Separate effect for setting initial values
    useEffect(() => {
        if (!attributes.username) {
            setAttributes({ 
                username: nhrccCoreContributionsCommonObj?.nhrccSettings?.username || '',
                preset: nhrccCoreContributionsCommonObj?.nhrccSettings?.preset || 'default'
            });
        }
    }, []); // Empty dependency array - runs once

    useEffect(() => {
        if (!attributes.username) {
            // setAttributes({ username: nhrccCoreContributionsCommonObj?.nhrccSettings?.username });
            setPreviewContent('');
            return;
        }

        setIsLoading(true);

        fetchPreview(attributes)
            .then(setPreviewContent)
            .catch((error) => {
                console.error('Failed to load preview:', error);
                setPreviewContent(`Error: ${error.message}`);
            })
            .finally(() => setIsLoading(false));
    }, [
        attributes.username,
        attributes.preset,
        attributes.backgroundColor,
        attributes.textColor,
        attributes.linkColor,
        attributes.borderColor,
        attributes.borderRadius,
        attributes.padding,
        attributes.margin,
        attributes.fontSize,
        attributes.fontWeight
    ]);

    return (
        <div {...blockProps}>
            <InspectorControls>
                <PanelBody title="Core Contributions Settings">
                    <TextControl
                        label="WordPress.org Username"
                        value={tempUsername}
                        onChange={(username) => {
                            setTempUsername(username);
                            updateUsernameDebounced(username);
                        }}
                        help="Enter your WordPress.org username to display contributions"
                    />
                    <SelectControl
                        label="Design Style"
                        value={attributes.preset}
                        options={PRESET_OPTIONS}
                        onChange={(preset) => setAttributes({ preset })}
                    />
                </PanelBody>

                <PanelColorSettings
                    title="Color Settings"
                    colorSettings={[
                        {
                            value: attributes.backgroundColor,
                            onChange: (backgroundColor) => setAttributes({ backgroundColor }),
                            label: 'Background Color',
                        },
                        {
                            value: attributes.textColor,
                            onChange: (textColor) => setAttributes({ textColor }),
                            label: 'Text Color',
                        },
                        {
                            value: attributes.linkColor,
                            onChange: (linkColor) => setAttributes({ linkColor }),
                            label: 'Link Color',
                        },
                        {
                            value: attributes.borderColor,
                            onChange: (borderColor) => setAttributes({ borderColor }),
                            label: 'Border Color',
                        },
                    ]}
                />

                <PanelBody title="Spacing Settings" initialOpen={false}>
                    {BoxControl && (
                        <>
                            <BoxControl
                                label="Padding"
                                values={attributes.padding}
                                onChange={(padding) => setAttributes({ padding })}
                            />
                            <BoxControl
                                label="Margin"
                                values={attributes.margin}
                                onChange={(margin) => setAttributes({ margin })}
                            />
                        </>
                    )}
                </PanelBody>

                <PanelBody title="Border Settings" initialOpen={false}>
                    <RangeControl
                        label="Border Radius"
                        value={attributes.borderRadius || 0}
                        onChange={(borderRadius) => setAttributes({ borderRadius })}
                        min={0}
                        max={50}
                    />
                </PanelBody>

                <PanelBody title="Typography Settings" initialOpen={false}>
                    {UnitControl && (
                        <UnitControl
                            label="Font Size"
                            value={attributes.fontSize}
                            onChange={(fontSize) => setAttributes({ fontSize })}
                        />
                    )}
                    <SelectControl
                        label="Font Weight"
                        value={attributes.fontWeight}
                        options={FONT_WEIGHT_OPTIONS}
                        onChange={(fontWeight) => setAttributes({ fontWeight })}
                    />
                </PanelBody>
            </InspectorControls>
            
            <PreviewContent 
                isLoading={isLoading}
                username={attributes.username}
                content={previewContent}
            />
        </div>
    );
}

// Constants
const PRESET_OPTIONS = [
    { label: 'Default', value: 'default' },
    { label: 'Minimal', value: 'minimal' },
];

const FONT_WEIGHT_OPTIONS = [
    { label: 'Normal', value: 'normal' },
    { label: 'Bold', value: 'bold' },
    { label: 'Light', value: '300' },
    { label: 'Medium', value: '500' },
    { label: 'Semi Bold', value: '600' },
    { label: 'Extra Bold', value: '800' },
];

const CACHE_DURATION_OPTIONS = [
    { label: '30 Minutes', value: 1800 },
    { label: '1 Hour', value: 3600 },
    { label: '6 Hours', value: 21600 },
    { label: '12 Hours', value: 43200 },
    { label: '24 Hours', value: 86400 },
];

// API and Helper Functions
async function fetchPreview(attributes) {
    try {
        const response = await apiFetch({
            path: '/nhrcc-core-contributions/v1/core-contributions/render',
            method: 'POST',
            data: attributes,
        });
        return response.content || '';
    } catch (error) {
        console.error('Failed to fetch preview:', error);
        throw new Error('Failed to load preview');
    }
}

// Presentational Components
function PreviewContent({ isLoading, username, content }) {
    if (isLoading) {
        return <Spinner />;
    }
    
    if (!username) {
        return (
            <div className="components-placeholder">
                <p>Please set a WordPress.org username to preview the contributions.</p>
            </div>
        );
    }
    
    return (
        <div 
            className="nhr-core-contributions-preview"
            dangerouslySetInnerHTML={{ __html: content }}
        />
    );
}