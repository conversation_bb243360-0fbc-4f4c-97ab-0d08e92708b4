/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/dashboard/src/SettingsPage.jsx":
/*!***********************************************!*\
  !*** ./assets/dashboard/src/SettingsPage.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api */ "./assets/dashboard/src/api.js");


const SettingsPage = () => {
  const [loading, setLoading] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [notification, setNotification] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [formData, setFormData] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)({
    username: '',
    preset: 'default',
    cacheDuration: 43200,
    // Ensure default is valid
    postsPerPage: 10 // Ensure number type
  });
  const [errors, setErrors] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)({});
  const validateForm = () => {
    const newErrors = {};
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleSave = async () => {
    if (!validateForm()) return;
    setLoading(true);
    try {
      const response = await (0,_api__WEBPACK_IMPORTED_MODULE_1__.updateSettings)(formData);
      setNotification({
        type: 'success',
        message: response.message
      });
    } catch (error) {
      setNotification({
        type: 'error',
        message: 'Error saving settings. Please try again later.'
      });
    } finally {
      setLoading(false);
    }
  };
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    const loadSettings = async () => {
      try {
        const data = await (0,_api__WEBPACK_IMPORTED_MODULE_1__.getSettings)();
        setFormData({
          ...data,
          cacheDuration: Number(data.cacheDuration) || 43200,
          // Ensure number
          postsPerPage: Number(data.postsPerPage) || 10 // Ensure number
        });
      } catch (error) {
        setNotification({
          type: 'error',
          message: 'Error loading settings'
        });
      }
    };
    loadSettings();
  }, []);
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('div', {
    className: 'nhrcc-core-contributions-settings-wrap'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('h1', {
    className: 'wp-heading-inline'
  }, 'Core Contributions Settings'), notification && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('div', {
    className: `notice notice-${notification.type === 'success' ? 'success' : 'error'} is-dismissible`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('p', null, notification.message), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('button', {
    type: 'button',
    className: 'notice-dismiss',
    onClick: () => setNotification(null)
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('span', {
    className: 'screen-reader-text'
  }, 'Dismiss this notice'))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('div', {
    className: 'card'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('h2', null, 'User Settings'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('p', {
    className: 'description'
  }, 'Configure the default user and display preferences'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('table', {
    className: 'form-table'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('tbody', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('tr', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('th', {
    scope: 'row'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('label', {
    htmlFor: 'username'
  }, 'Default WordPress.org Username')), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('td', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('input', {
    type: 'text',
    id: 'username',
    className: 'regular-text1 w-full',
    value: formData.username,
    onChange: e => setFormData({
      ...formData,
      username: e.target.value
    })
  }), errors.username && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('p', {
    className: 'description error'
  }, errors.username), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('p', {
    className: 'description'
  }, 'This username will be used when no specific user is provided')))))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('div', {
    className: 'card'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('h2', null, 'Display Settings'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('p', {
    className: 'description'
  }, 'Customize how contributions are displayed'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('table', {
    className: 'form-table'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('tbody', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('tr', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('th', {
    scope: 'row'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('label', {
    htmlFor: 'preset'
  }, 'Preset')), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('td', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('select', {
    id: 'preset',
    className: 'w-full',
    value: formData.preset,
    onChange: e => setFormData({
      ...formData,
      preset: e.target.value
    })
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('option', {
    value: 'default'
  }, 'Default'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('option', {
    value: 'minimal'
  }, 'Minimal')))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('tr', {
    className: 'hidden'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('th', {
    scope: 'row'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('label', {
    htmlFor: 'postsPerPage'
  }, 'Posts Per Page')), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('td', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('input', {
    type: 'number',
    id: 'postsPerPage',
    className: 'small-text1 w-full',
    value: formData.postsPerPage,
    onChange: e => setFormData({
      ...formData,
      postsPerPage: Number(e.target.value)
    })
  }), errors.postsPerPage && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('p', {
    className: 'description error'
  }, errors.postsPerPage)))))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('div', {
    className: 'card'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('h2', null, 'Cache Settings'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('p', {
    className: 'description'
  }, 'Manage how long contribution data is stored'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('table', {
    className: 'form-table'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('tbody', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('tr', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('th', {
    scope: 'row'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('label', {
    htmlFor: 'cacheDuration'
  }, 'Cache Duration')), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('td', null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('select', {
    id: 'cacheDuration',
    className: 'w-full',
    value: formData.cacheDuration,
    onChange: e => setFormData({
      ...formData,
      cacheDuration: Number(e.target.value)
    })
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('option', {
    value: '1800'
  }, '30 Minutes'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('option', {
    value: '3600'
  }, '1 Hour'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('option', {
    value: '21600'
  }, '6 Hours'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('option', {
    value: '43200'
  }, '12 Hours'), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('option', {
    value: '86400'
  }, '24 Hours'))))))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('p', {
    className: 'submit'
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)('button', {
    type: 'button',
    className: 'button button-primary',
    onClick: handleSave,
    disabled: loading
  }, loading ? 'Saving...' : 'Save Changes')));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);

/***/ }),

/***/ "./assets/dashboard/src/api.js":
/*!*************************************!*\
  !*** ./assets/dashboard/src/api.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getSettings: () => (/* binding */ getSettings),
/* harmony export */   updateSettings: () => (/* binding */ updateSettings)
/* harmony export */ });
/* harmony import */ var _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/api-fetch */ "@wordpress/api-fetch");
/* harmony import */ var _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0__);

const getSettings = async () => {
  try {
    const settings = await _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0___default()({
      path: '/nhrcc-core-contributions/v1/settings',
      method: 'GET'
    });
    return settings;
  } catch (error) {
    console.error('Error fetching settings:', error);
    throw error;
  }
};
const updateSettings = async settings => {
  try {
    return await _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0___default()({
      path: '/nhrcc-core-contributions/v1/settings',
      method: 'POST',
      data: settings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    throw error;
  }
};

/***/ }),

/***/ "@wordpress/api-fetch":
/*!**********************************!*\
  !*** external ["wp","apiFetch"] ***!
  \**********************************/
/***/ ((module) => {

module.exports = window["wp"]["apiFetch"];

/***/ }),

/***/ "@wordpress/element":
/*!*********************************!*\
  !*** external ["wp","element"] ***!
  \*********************************/
/***/ ((module) => {

module.exports = window["wp"]["element"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!***************************************!*\
  !*** ./assets/dashboard/src/index.js ***!
  \***************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _SettingsPage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SettingsPage */ "./assets/dashboard/src/SettingsPage.jsx");



// Initialize the app
const init = () => {
  const container = document.getElementById('nhrcc-admin-settings');
  if (container) {
    const root = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createRoot)(container);
    root.render((0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_SettingsPage__WEBPACK_IMPORTED_MODULE_1__["default"])); // Use createElement to ensure JSX works properly
  }
};

// Wait for DOM to be ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}
})();

/******/ })()
;
//# sourceMappingURL=index.js.map