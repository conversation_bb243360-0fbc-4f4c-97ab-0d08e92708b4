{"version": 3, "file": "index.js", "mappings": ";;;;;;;;;;;;;;;;;AAAwE;AACpB;AAEpD,MAAMK,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGP,4DAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGT,4DAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,4DAAQ,CAAC;IACvCY,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,KAAK;IAAE;IACtBC,YAAY,EAAE,EAAE,CAAE;EACpB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGjB,4DAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,IAAI,CAACT,QAAQ,CAACE,QAAQ,CAACQ,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACP,QAAQ,GAAG,sBAAsB;IAC7C;IACAK,SAAS,CAACE,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErBX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkB,QAAQ,GAAG,MAAMrB,oDAAc,CAACM,QAAQ,CAAC;MAC/CD,eAAe,CAAC;QAAEiB,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAEF,QAAQ,CAACE;MAAQ,CAAC,CAAC;IACjE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdnB,eAAe,CAAC;QAAEiB,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAiD,CAAC,CAAC;IAC/F,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDN,6DAAS,CAAC,MAAM;IACd,MAAM4B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,IAAI,GAAG,MAAM3B,iDAAW,CAAC,CAAC;QAChCQ,WAAW,CAAC;UACV,GAAGmB,IAAI;UACPhB,aAAa,EAAEiB,MAAM,CAACD,IAAI,CAAChB,aAAa,CAAC,IAAI,KAAK;UAAE;UACpDC,YAAY,EAAEgB,MAAM,CAACD,IAAI,CAACf,YAAY,CAAC,IAAI,EAAE,CAAE;QACjD,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdnB,eAAe,CAAC;UAAEiB,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAyB,CAAC,CAAC;MACvE;IACF,CAAC;IAEDE,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO3B,iEAAa,CAClB,KAAK,EACL;IAAE8B,SAAS,EAAE;EAAyC,CAAC,EACvD9B,iEAAa,CAAC,IAAI,EAAE;IAAE8B,SAAS,EAAE;EAAoB,CAAC,EAAE,6BAA6B,CAAC,EACtFxB,YAAY,IACVN,iEAAa,CACX,KAAK,EACL;IAAE8B,SAAS,EAAE,iBAAiBxB,YAAY,CAACkB,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;EAAkB,CAAC,EACtGxB,iEAAa,CAAC,GAAG,EAAE,IAAI,EAAEM,YAAY,CAACmB,OAAO,CAAC,EAC9CzB,iEAAa,CACX,QAAQ,EACR;IAAEwB,IAAI,EAAE,QAAQ;IAAEM,SAAS,EAAE,gBAAgB;IAAEC,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAAC,IAAI;EAAE,CAAC,EACrFP,iEAAa,CAAC,MAAM,EAAE;IAAE8B,SAAS,EAAE;EAAqB,CAAC,EAAE,qBAAqB,CAClF,CACF,CAAC,EACH9B,iEAAa,CACX,KAAK,EACL;IAAE8B,SAAS,EAAE;EAAO,CAAC,EACrB9B,iEAAa,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,EAC1CA,iEAAa,CAAC,GAAG,EAAE;IAAE8B,SAAS,EAAE;EAAc,CAAC,EAAE,oDAAoD,CAAC,EACtG9B,iEAAa,CACX,OAAO,EACP;IAAE8B,SAAS,EAAE;EAAa,CAAC,EAC3B9B,iEAAa,CAAC,OAAO,EAAE,IAAI,EACzBA,iEAAa,CAAC,IAAI,EAAE,IAAI,EACtBA,iEAAa,CAAC,IAAI,EAAE;IAAEgC,KAAK,EAAE;EAAM,CAAC,EAClChC,iEAAa,CAAC,OAAO,EAAE;IAAEiC,OAAO,EAAE;EAAW,CAAC,EAAE,gCAAgC,CAClF,CAAC,EACDjC,iEAAa,CAAC,IAAI,EAAE,IAAI,EACtBA,iEAAa,CAAC,OAAO,EAAE;IACrBwB,IAAI,EAAE,MAAM;IACZU,EAAE,EAAE,UAAU;IACdJ,SAAS,EAAE,sBAAsB;IACjCK,KAAK,EAAE3B,QAAQ,CAACE,QAAQ;IACxB0B,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,QAAQ,EAAE2B,CAAC,CAACC,MAAM,CAACH;IAAM,CAAC;EACxE,CAAC,CAAC,EACFrB,MAAM,CAACJ,QAAQ,IAAIV,iEAAa,CAAC,GAAG,EAAE;IAAE8B,SAAS,EAAE;EAAoB,CAAC,EAAEhB,MAAM,CAACJ,QAAQ,CAAC,EAC1FV,iEAAa,CAAC,GAAG,EAAE;IAAE8B,SAAS,EAAE;EAAc,CAAC,EAAE,8DAA8D,CACjH,CACF,CACF,CACF,CACF,CAAC,EACD9B,iEAAa,CACX,KAAK,EACL;IAAE8B,SAAS,EAAE;EAAO,CAAC,EACrB9B,iEAAa,CAAC,IAAI,EAAE,IAAI,EAAE,kBAAkB,CAAC,EAC7CA,iEAAa,CAAC,GAAG,EAAE;IAAE8B,SAAS,EAAE;EAAc,CAAC,EAAE,2CAA2C,CAAC,EAC7F9B,iEAAa,CACX,OAAO,EACP;IAAE8B,SAAS,EAAE;EAAa,CAAC,EAC3B9B,iEAAa,CAAC,OAAO,EAAE,IAAI,EACzBA,iEAAa,CAAC,IAAI,EAAE,IAAI,EACtBA,iEAAa,CAAC,IAAI,EAAE;IAAEgC,KAAK,EAAE;EAAM,CAAC,EAClChC,iEAAa,CAAC,OAAO,EAAE;IAAEiC,OAAO,EAAE;EAAS,CAAC,EAAE,QAAQ,CACxD,CAAC,EACDjC,iEAAa,CAAC,IAAI,EAAE,IAAI,EACtBA,iEAAa,CAAC,QAAQ,EAAE;IACtBkC,EAAE,EAAE,QAAQ;IACZJ,SAAS,EAAE,QAAQ;IACnBK,KAAK,EAAE3B,QAAQ,CAACG,MAAM;IACtByB,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEG,MAAM,EAAE0B,CAAC,CAACC,MAAM,CAACH;IAAM,CAAC;EACtE,CAAC,EACCnC,iEAAa,CAAC,QAAQ,EAAE;IAAEmC,KAAK,EAAE;EAAU,CAAC,EAAE,SAAS,CAAC,EACxDnC,iEAAa,CAAC,QAAQ,EAAE;IAAEmC,KAAK,EAAE;EAAU,CAAC,EAAE,SAAS,CACzD,CACF,CACF,CAAC,EACDnC,iEAAa,CAAC,IAAI,EAAE;IAAE8B,SAAS,EAAE;EAAS,CAAC,EACzC9B,iEAAa,CAAC,IAAI,EAAE;IAAEgC,KAAK,EAAE;EAAM,CAAC,EAClChC,iEAAa,CAAC,OAAO,EAAE;IAAEiC,OAAO,EAAE;EAAe,CAAC,EAAE,gBAAgB,CACtE,CAAC,EACDjC,iEAAa,CAAC,IAAI,EAAE,IAAI,EACtBA,iEAAa,CAAC,OAAO,EAAE;IACrBwB,IAAI,EAAE,QAAQ;IACdU,EAAE,EAAE,cAAc;IAClBJ,SAAS,EAAE,oBAAoB;IAC/BK,KAAK,EAAE3B,QAAQ,CAACK,YAAY;IAC5BuB,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEK,YAAY,EAAEgB,MAAM,CAACQ,CAAC,CAACC,MAAM,CAACH,KAAK;IAAE,CAAC;EACpF,CAAC,CAAC,EACFrB,MAAM,CAACD,YAAY,IAAIb,iEAAa,CAAC,GAAG,EAAE;IAAE8B,SAAS,EAAE;EAAoB,CAAC,EAAEhB,MAAM,CAACD,YAAY,CACnG,CACF,CACF,CACF,CACF,CAAC,EACDb,iEAAa,CACX,KAAK,EACL;IAAE8B,SAAS,EAAE;EAAO,CAAC,EACrB9B,iEAAa,CAAC,IAAI,EAAE,IAAI,EAAE,gBAAgB,CAAC,EAC3CA,iEAAa,CAAC,GAAG,EAAE;IAAE8B,SAAS,EAAE;EAAc,CAAC,EAAE,6CAA6C,CAAC,EAC/F9B,iEAAa,CACX,OAAO,EACP;IAAE8B,SAAS,EAAE;EAAa,CAAC,EAC3B9B,iEAAa,CAAC,OAAO,EAAE,IAAI,EACzBA,iEAAa,CAAC,IAAI,EAAE,IAAI,EACtBA,iEAAa,CAAC,IAAI,EAAE;IAAEgC,KAAK,EAAE;EAAM,CAAC,EAClChC,iEAAa,CAAC,OAAO,EAAE;IAAEiC,OAAO,EAAE;EAAgB,CAAC,EAAE,gBAAgB,CACvE,CAAC,EACDjC,iEAAa,CAAC,IAAI,EAAE,IAAI,EACtBA,iEAAa,CAAC,QAAQ,EAAE;IACtBkC,EAAE,EAAE,eAAe;IACnBJ,SAAS,EAAE,QAAQ;IACnBK,KAAK,EAAE3B,QAAQ,CAACI,aAAa;IAC7BwB,QAAQ,EAAGC,CAAC,IAAK5B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEI,aAAa,EAAEiB,MAAM,CAACQ,CAAC,CAACC,MAAM,CAACH,KAAK;IAAE,CAAC;EACrF,CAAC,EACCnC,iEAAa,CAAC,QAAQ,EAAE;IAAEmC,KAAK,EAAE;EAAO,CAAC,EAAE,YAAY,CAAC,EACxDnC,iEAAa,CAAC,QAAQ,EAAE;IAAEmC,KAAK,EAAE;EAAO,CAAC,EAAE,QAAQ,CAAC,EACpDnC,iEAAa,CAAC,QAAQ,EAAE;IAAEmC,KAAK,EAAE;EAAQ,CAAC,EAAE,SAAS,CAAC,EACtDnC,iEAAa,CAAC,QAAQ,EAAE;IAAEmC,KAAK,EAAE;EAAQ,CAAC,EAAE,UAAU,CAAC,EACvDnC,iEAAa,CAAC,QAAQ,EAAE;IAAEmC,KAAK,EAAE;EAAQ,CAAC,EAAE,UAAU,CACxD,CACF,CACF,CACF,CACF,CACF,CAAC,EACDnC,iEAAa,CACX,GAAG,EACH;IAAE8B,SAAS,EAAE;EAAS,CAAC,EACvB9B,iEAAa,CAAC,QAAQ,EAAE;IACtBwB,IAAI,EAAE,QAAQ;IACdM,SAAS,EAAE,uBAAuB;IAClCC,OAAO,EAAET,UAAU;IACnBiB,QAAQ,EAAEnC;EACZ,CAAC,EAAEA,OAAO,GAAG,WAAW,GAAG,cAAc,CAC3C,CACF,CAAC;AACH,CAAC;AAED,iEAAeD,YAAY;;;;;;;;;;;;;;;;;ACxLiB;AAErC,MAAMF,WAAW,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACA,MAAMwC,QAAQ,GAAG,MAAMD,2DAAQ,CAAC;MAC5BE,IAAI,EAAE,uCAAuC;MAC7CC,MAAM,EAAE;IACZ,CAAC,CAAC;IAEF,OAAOF,QAAQ;EAEnB,CAAC,CAAC,OAAOf,KAAK,EAAE;IACZkB,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACf;AACJ,CAAC;AAEM,MAAMxB,cAAc,GAAG,MAAOuC,QAAQ,IAAK;EAC9C,IAAI;IACA,OAAO,MAAMD,2DAAQ,CAAC;MAClBE,IAAI,EAAE,uCAAuC;MAC7CC,MAAM,EAAE,MAAM;MACdf,IAAI,EAAEa;IACV,CAAC,CAAC;EACN,CAAC,CAAC,OAAOf,KAAK,EAAE;IACZkB,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACf;AACJ,CAAC;;;;;;;;;;AC5BD;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;ACN+D;AACrB;;AAE1C;AACA,MAAMoB,IAAI,GAAGA,CAAA,KAAM;EACf,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC;EACjE,IAAIF,SAAS,EAAE;IACX,MAAMG,IAAI,GAAGL,8DAAU,CAACE,SAAS,CAAC;IAClCG,IAAI,CAACC,MAAM,CAACnD,iEAAa,CAACG,qDAAY,CAAC,CAAC,CAAC,CAAC;EAC9C;AACJ,CAAC;;AAED;AACA,IAAI6C,QAAQ,CAACI,UAAU,KAAK,SAAS,EAAE;EACnCJ,QAAQ,CAACK,gBAAgB,CAAC,kBAAkB,EAAEP,IAAI,CAAC;AACvD,CAAC,MAAM;EACHA,IAAI,CAAC,CAAC;AACV,C", "sources": ["webpack://nhrrob-core-contributions/./assets/dashboard/src/SettingsPage.jsx", "webpack://nhrrob-core-contributions/./assets/dashboard/src/api.js", "webpack://nhrrob-core-contributions/external window [\"wp\",\"apiFetch\"]", "webpack://nhrrob-core-contributions/external window [\"wp\",\"element\"]", "webpack://nhrrob-core-contributions/webpack/bootstrap", "webpack://nhrrob-core-contributions/webpack/runtime/compat get default export", "webpack://nhrrob-core-contributions/webpack/runtime/define property getters", "webpack://nhrrob-core-contributions/webpack/runtime/hasOwnProperty shorthand", "webpack://nhrrob-core-contributions/webpack/runtime/make namespace object", "webpack://nhrrob-core-contributions/./assets/dashboard/src/index.js"], "sourcesContent": ["import { useState, useEffect, createElement } from '@wordpress/element';\nimport { getSettings, updateSettings } from './api';\n\nconst SettingsPage = () => {\n  const [loading, setLoading] = useState(false);\n  const [notification, setNotification] = useState(null);\n  const [formData, setFormData] = useState({\n    username: '',\n    preset: 'default',\n    cacheDuration: 43200, // Ensure default is valid\n    postsPerPage: 10, // Ensure number type\n  });\n  const [errors, setErrors] = useState({});\n\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSave = async () => {\n    if (!validateForm()) return;\n\n    setLoading(true);\n    try {\n      const response = await updateSettings(formData);\n      setNotification({ type: 'success', message: response.message });\n    } catch (error) {\n      setNotification({ type: 'error', message: 'Error saving settings. Please try again later.' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    const loadSettings = async () => {\n      try {\n        const data = await getSettings();\n        setFormData({\n          ...data,\n          cacheDuration: Number(data.cacheDuration) || 43200, // Ensure number\n          postsPerPage: Number(data.postsPerPage) || 10, // Ensure number\n        });\n      } catch (error) {\n        setNotification({ type: 'error', message: 'Error loading settings' });\n      }\n    };\n\n    loadSettings();\n  }, []);\n\n  return createElement(\n    'div',\n    { className: 'nhrcc-core-contributions-settings-wrap' },\n    createElement('h1', { className: 'wp-heading-inline' }, 'Core Contributions Settings'),\n    notification &&\n      createElement(\n        'div',\n        { className: `notice notice-${notification.type === 'success' ? 'success' : 'error'} is-dismissible` },\n        createElement('p', null, notification.message),\n        createElement(\n          'button',\n          { type: 'button', className: 'notice-dismiss', onClick: () => setNotification(null) },\n          createElement('span', { className: 'screen-reader-text' }, 'Dismiss this notice')\n        )\n      ),\n    createElement(\n      'div',\n      { className: 'card' },\n      createElement('h2', null, 'User Settings'),\n      createElement('p', { className: 'description' }, 'Configure the default user and display preferences'),\n      createElement(\n        'table',\n        { className: 'form-table' },\n        createElement('tbody', null,\n          createElement('tr', null,\n            createElement('th', { scope: 'row' },\n              createElement('label', { htmlFor: 'username' }, 'Default WordPress.org Username')\n            ),\n            createElement('td', null,\n              createElement('input', {\n                type: 'text',\n                id: 'username',\n                className: 'regular-text1 w-full',\n                value: formData.username,\n                onChange: (e) => setFormData({ ...formData, username: e.target.value })\n              }),\n              errors.username && createElement('p', { className: 'description error' }, errors.username),\n              createElement('p', { className: 'description' }, 'This username will be used when no specific user is provided')\n            )\n          )\n        )\n      )\n    ),\n    createElement(\n      'div',\n      { className: 'card' },\n      createElement('h2', null, 'Display Settings'),\n      createElement('p', { className: 'description' }, 'Customize how contributions are displayed'),\n      createElement(\n        'table',\n        { className: 'form-table' },\n        createElement('tbody', null,\n          createElement('tr', null,\n            createElement('th', { scope: 'row' },\n              createElement('label', { htmlFor: 'preset' }, 'Preset')\n            ),\n            createElement('td', null,\n              createElement('select', {\n                id: 'preset',\n                className: 'w-full',\n                value: formData.preset,\n                onChange: (e) => setFormData({ ...formData, preset: e.target.value })\n              },\n                createElement('option', { value: 'default' }, 'Default'),\n                createElement('option', { value: 'minimal' }, 'Minimal')\n              )\n            )\n          ),\n          createElement('tr', { className: 'hidden' },\n            createElement('th', { scope: 'row' },\n              createElement('label', { htmlFor: 'postsPerPage' }, 'Posts Per Page')\n            ),\n            createElement('td', null,\n              createElement('input', {\n                type: 'number',\n                id: 'postsPerPage',\n                className: 'small-text1 w-full',\n                value: formData.postsPerPage,\n                onChange: (e) => setFormData({ ...formData, postsPerPage: Number(e.target.value) })\n              }),\n              errors.postsPerPage && createElement('p', { className: 'description error' }, errors.postsPerPage)\n            )\n          )\n        )\n      )\n    ),\n    createElement(\n      'div',\n      { className: 'card' },\n      createElement('h2', null, 'Cache Settings'),\n      createElement('p', { className: 'description' }, 'Manage how long contribution data is stored'),\n      createElement(\n        'table',\n        { className: 'form-table' },\n        createElement('tbody', null,\n          createElement('tr', null,\n            createElement('th', { scope: 'row' },\n              createElement('label', { htmlFor: 'cacheDuration' }, 'Cache Duration')\n            ),\n            createElement('td', null,\n              createElement('select', {\n                id: 'cacheDuration',\n                className: 'w-full',\n                value: formData.cacheDuration,\n                onChange: (e) => setFormData({ ...formData, cacheDuration: Number(e.target.value) })\n              },\n                createElement('option', { value: '1800' }, '30 Minutes'),\n                createElement('option', { value: '3600' }, '1 Hour'),\n                createElement('option', { value: '21600' }, '6 Hours'),\n                createElement('option', { value: '43200' }, '12 Hours'),\n                createElement('option', { value: '86400' }, '24 Hours')\n              )\n            )\n          )\n        )\n      )\n    ),\n    createElement(\n      'p',\n      { className: 'submit' },\n      createElement('button', {\n        type: 'button',\n        className: 'button button-primary',\n        onClick: handleSave,\n        disabled: loading\n      }, loading ? 'Saving...' : 'Save Changes')\n    )\n  );\n};\n\nexport default SettingsPage;\n", "import apiFetch from '@wordpress/api-fetch';\n\nexport const getSettings = async () => {\n    try {\n        const settings = await apiFetch({ \n            path: '/nhrcc-core-contributions/v1/settings',\n            method: 'GET',\n        });\n\n        return settings;\n\n    } catch (error) {\n        console.error('Error fetching settings:', error);\n        throw error;\n    }\n};\n\nexport const updateSettings = async (settings) => {\n    try {\n        return await apiFetch({\n            path: '/nhrcc-core-contributions/v1/settings',\n            method: 'POST',\n            data: settings\n        });\n    } catch (error) {\n        console.error('Error updating settings:', error);\n        throw error;\n    }\n};", "module.exports = window[\"wp\"][\"apiFetch\"];", "module.exports = window[\"wp\"][\"element\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { createRoot, createElement } from '@wordpress/element';\nimport SettingsPage from './SettingsPage';\n\n// Initialize the app\nconst init = () => {\n    const container = document.getElementById('nhrcc-admin-settings');\n    if (container) {\n        const root = createRoot(container);\n        root.render(createElement(SettingsPage)); // Use createElement to ensure JSX works properly\n    }\n};\n\n// Wait for DOM to be ready\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', init);\n} else {\n    init();\n}"], "names": ["useState", "useEffect", "createElement", "getSettings", "updateSettings", "SettingsPage", "loading", "setLoading", "notification", "setNotification", "formData", "setFormData", "username", "preset", "cacheDuration", "postsPerPage", "errors", "setErrors", "validateForm", "newErrors", "trim", "Object", "keys", "length", "handleSave", "response", "type", "message", "error", "loadSettings", "data", "Number", "className", "onClick", "scope", "htmlFor", "id", "value", "onChange", "e", "target", "disabled", "apiFetch", "settings", "path", "method", "console", "createRoot", "init", "container", "document", "getElementById", "root", "render", "readyState", "addEventListener"], "sourceRoot": ""}